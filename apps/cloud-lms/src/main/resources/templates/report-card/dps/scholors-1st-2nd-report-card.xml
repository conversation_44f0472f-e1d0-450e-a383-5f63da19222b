<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="17mm" />
            <fo:region-before margin="8mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">

        <fo:static-content flow-name="xsl-region-before">
            <fo:block-container width="100%" height="93%" margin-top="12mm" border="2pt solid #261b6b" padding="6mm"
                                padding-left="-6mm" padding-bottom="250mm" padding-right="-6mm" >
                <fo:block>

                </fo:block>
            </fo:block-container>
        </fo:static-content>

        <fo:flow flow-name="xsl-region-body" >

            <fo:block  padding="10mm" margin-top="-10mm">

                <fo:table border="none">
                    <fo:table-column column-width="26mm" />
                    <fo:table-column column-width="126mm" />
                    <fo:table-body >
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block margin-left="-6mm" padding-top="-2mm">
                                    <fo:external-graphic src='url("https://images.wexledu.com/live-worksheets/the779154/20250109042554703.svg")'
                                                         content-width="150px"  content-height="150px" />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="20pt" font-weight="bold" text-align="center" color="#261b6b" padding-top="-2mm" space-after="3pt" th:text="${model.header.schoolName}"> </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" text-align="center" color="#261b6b" space-after="3pt" th:text="${model.header.address}"> </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" text-align="center" space-after="3pt">WEBSITE: www.ps.tsh.edu.in, MOBILE NO.: 9218423356,8628800056</fo:block>
                                <fo:block font-size="8pt" font-weight="bold" text-align="center" color="#261b6b" space-after="5pt">(AFFILIATION NO.: 630111)</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table margin-left="-4mm" margin-right="-12mm" width="100%">
                    <fo:table-column column-width="100%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="8pt" border-top="0.5pt solid black" border-bottom="0.5pt solid black" background-color="#fcc5f1" text-align="center" padding="2mm">
                                    ACHIEVEMENT RECORD (ACADEMIC YEAR 2024-25)
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table margin-left="-3mm" border="none" font-size="7pt" space-before="5pt" space-after="5pt">
                    <fo:table-column column-width="90mm"/>
                    <fo:table-column column-width="55mm"/>
                    <fo:table-column column-width="40mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">STUDENT'S NAME:
                                    <fo:inline font-weight="normal" th:text="${model.body.name}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">CLASS &amp; SECTION:
                                    <fo:inline font-weight="normal" th:text="${model.body.sectionName}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">ADMISSION NO.:
                                    <fo:inline font-weight="normal" th:text="${model.body.admissionNumber}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">FATHER'S NAME:
                                    <fo:inline font-weight="normal" th:text="${model.body.fatherName}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">D.O.B.:
                                    <fo:inline font-weight="normal" th:text="${model.body.dateOfBirth}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">ROLL NO.:
                                    <fo:inline font-weight="normal" th:text="${model.body.rollNo}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">MOTHER'S NAME:
                                    <fo:inline font-weight="normal" th:text="${model.body.motherName}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">BLOOD GROUP:
                                    <fo:inline font-weight="normal" th:text="${model.body.bloodGroup}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block padding-top="4pt" font-weight="bold">PHONE NO.:
                                    <fo:inline font-weight="normal" th:text="${model.body.mobileNumber}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell number-columns-spanned="3">
                                <fo:block padding-top="4pt" font-weight="bold">ADDRESS:
                                    <fo:inline font-weight="normal" th:text="${model.body.address}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table margin-left="-4mm" margin-right="-12mm" width="100%" space-before="2pt">
                    <fo:table-column column-width="100%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="8pt" border-top="0.5pt solid black" border-bottom="0.5pt solid black" background-color="#f2d5e9" text-align="center" padding="2mm">
                                    A. LANGUAGES
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>


                <fo:block font-size="8pt" space-before="4mm" margin-left="-4mm" margin-right="-4mm" th:if="${model.body.subjects.size() > 0}">
                    <fo:table table-layout="fixed" width="100%">
                        <fo:table-column column-width="47%"/>
                        <fo:table-column column-width="6%"/>
                        <fo:table-column column-width="47%"/>

                        <fo:table-body>
                            <fo:table-row>
                                <!-- Left Table: Odd-indexed subjects (0, 2, 4...) -->
                                <fo:table-cell>
                                    <fo:block>
                                        <th:block th:each="i : ${#numbers.sequence(0, model.body.subjects.size() - 1)}"
                                                  th:if="${i % 2 == 0}">
                                            <th:block th:with="subject=${model.body.subjects[i]}">
                                                <!-- Subject Header -->
                                                <fo:table table-layout="fixed" width="100%">
                                                    <fo:table-column column-width="80%"/>
                                                    <fo:table-column column-width="20%"/>
                                                    <fo:table-body>
                                                        <fo:table-row>
                                                            <fo:table-cell background-color="#f2d5e9" padding="1mm" border="1pt solid black">
                                                                <fo:block margin-left="4mm" margin-right="4mm" th:text="${subject.name}"/>
                                                            </fo:table-cell>
                                                            <fo:table-cell background-color="#f2d5e9" padding="1mm" border="1pt solid black">
                                                                <fo:block margin-left="4mm" margin-right="4mm" text-align="center" th:text="${subject.term}"/>
                                                            </fo:table-cell>
                                                        </fo:table-row>
                                                    </fo:table-body>
                                                </fo:table>

                                                <!-- Skill Groups -->
                                                <th:block th:each="group : ${subject.skillGroups}">
                                                    <fo:table table-layout="fixed" width="100%">
                                                        <fo:table-column column-width="80%"/>
                                                        <fo:table-column column-width="20%"/>
                                                        <fo:table-body>
                                                            <fo:table-row th:if="${group.groupName != null}">
                                                                <fo:table-cell padding="1mm" border="1pt solid black" font-weight="bold">
                                                                    <fo:block margin-left="4mm" margin-right="4mm" th:text="${group.groupName}"/>
                                                                </fo:table-cell>
                                                                <fo:table-cell padding="1mm" border="1pt solid black"><fo:block/></fo:table-cell>
                                                            </fo:table-row>

                                                            <!-- Skill Rows -->
                                                            <th:block th:each="skill : ${group.skills}">
                                                                <fo:table-row>
                                                                    <fo:table-cell padding="1mm" border="1pt solid black">
                                                                        <fo:block margin-left="4mm" margin-right="4mm" th:text="${skill.name}"/>
                                                                    </fo:table-cell>
                                                                    <fo:table-cell padding="1mm" border="1pt solid black">
                                                                        <fo:block margin-left="4mm" margin-right="4mm" text-align="center" th:text="${skill.grade}"/>
                                                                    </fo:table-cell>
                                                                </fo:table-row>
                                                            </th:block>
                                                        </fo:table-body>
                                                    </fo:table>
                                                    <fo:block space-before="2mm"/>
                                                </th:block>
                                            </th:block>
                                        </th:block>
                                    </fo:block>
                                </fo:table-cell>

                                <!-- Spacer -->
                                <fo:table-cell><fo:block margin-left="-4mm" margin-right="-4mm"> </fo:block></fo:table-cell>

                                <!-- Right Table: Even-indexed subjects (1, 3, 5...) -->
                                <fo:table-cell>
                                    <fo:block>
                                        <th:block th:each="i : ${#numbers.sequence(0, model.body.subjects.size() - 1)}"
                                                  th:if="${i % 2 == 1}">
                                            <th:block th:with="subject=${model.body.subjects[i]}">
                                                <!-- Subject Header -->
                                                <fo:table table-layout="fixed" width="100%">
                                                    <fo:table-column column-width="80%"/>
                                                    <fo:table-column column-width="20%"/>
                                                    <fo:table-body>
                                                        <fo:table-row>
                                                            <fo:table-cell background-color="#f2d5e9" padding="1mm" border="1pt solid black">
                                                                <fo:block margin-left="4mm" margin-right="4mm" th:text="${subject.name}"/>
                                                            </fo:table-cell>
                                                            <fo:table-cell background-color="#f2d5e9" padding="1mm" border="1pt solid black">
                                                                <fo:block margin-left="4mm" margin-right="4mm" text-align="center" th:text="${subject.term}"/>
                                                            </fo:table-cell>
                                                        </fo:table-row>
                                                    </fo:table-body>
                                                </fo:table>

                                                <!-- Skill Groups -->
                                                <th:block th:each="group : ${subject.skillGroups}">
                                                    <fo:table table-layout="fixed" width="100%">
                                                        <fo:table-column column-width="80%"/>
                                                        <fo:table-column column-width="20%"/>
                                                        <fo:table-body>
                                                            <fo:table-row th:if="${group.groupName != null}">
                                                                <fo:table-cell padding="1mm" border="1pt solid black" font-weight="bold">
                                                                    <fo:block margin-left="4mm" margin-right="4mm" th:text="${group.groupName}"/>
                                                                </fo:table-cell>
                                                                <fo:table-cell padding="1mm" border="1pt solid black"><fo:block/></fo:table-cell>
                                                            </fo:table-row>

                                                            <!-- Skill Rows -->
                                                            <th:block th:each="skill : ${group.skills}">
                                                                <fo:table-row>
                                                                    <fo:table-cell padding="1mm" border="1pt solid black">
                                                                        <fo:block margin-left="4mm" margin-right="4mm" th:text="${skill.name}"/>
                                                                    </fo:table-cell>
                                                                    <fo:table-cell padding="1mm" border="1pt solid black">
                                                                        <fo:block margin-left="4mm" margin-right="4mm" text-align="center" th:text="${skill.grade}"/>
                                                                    </fo:table-cell>
                                                                </fo:table-row>
                                                            </th:block>
                                                        </fo:table-body>
                                                    </fo:table>
                                                    <fo:block space-before="2mm"/>
                                                </th:block>
                                            </th:block>
                                        </th:block>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block font-size="8pt" space-before="4mm" margin-left="-9mm" margin-right="-10mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="100%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell background-color="#f2d5e9" padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">ATTENDANCE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="2mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center" th:text="${model.body.attendance}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:table margin-left="-4mm" margin-right="-12mm" width="100%" space-before="5pt">
                    <fo:table-column column-width="100%"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="8pt" border-top="0.5pt solid black" border-bottom="0.5pt solid black" background-color="#f2d5e9" text-align="center" padding="2mm">
                                    GENERAL REMARK
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block font-size="8pt" border-bottom="0.5pt solid black" text-align="left" padding="2mm"
                                          th:text="${model.body.remarks}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>


                <fo:block font-size="8pt" space-before="4mm" margin-left="-9mm" >
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse">
                        <fo:table-column column-width="18%"/>
                        <fo:table-column column-width="19%"/>
                        <fo:table-column column-width="21%"/>
                        <fo:table-column column-width="1%"/>
                        <fo:table-column column-width="21%"/>
                        <fo:table-column column-width="22.5%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell background-color="#f2d5e9" padding="1mm" border="1pt solid black" number-columns-spanned="6">
                                    <fo:block margin-left="10mm" text-align="center">KEY TO GRADE (SCHOLASTIC/CO-SCHOLASTIC)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">A+</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">90% - 100%</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">Outstanding</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">G</fo:block>
                                </fo:table-cell><fo:table-cell padding="1mm" border="1pt solid black">
                                <fo:block margin-left="10mm" text-align="center">Good</fo:block>
                            </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">A</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">75% - 89%</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">Excellent</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">A</fo:block>
                                </fo:table-cell><fo:table-cell padding="1mm" border="1pt solid black">
                                <fo:block margin-left="10mm" text-align="center">Average</fo:block>
                            </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">B</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">56% - 74%</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">Very Good</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">N</fo:block>
                                </fo:table-cell><fo:table-cell padding="1mm" border="1pt solid black">
                                <fo:block margin-left="10mm" text-align="center">Needs Improvement</fo:block>
                            </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">C</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">35% - 55%</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">Good</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">H</fo:block>
                                </fo:table-cell><fo:table-cell padding="1mm" border="1pt solid black">
                                <fo:block margin-left="10mm" text-align="center">High</fo:block>
                            </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">D</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">Below 35%</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">Scope for Improvement</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border="1pt solid black">
                                    <fo:block margin-left="10mm" text-align="center">L</fo:block>
                                </fo:table-cell><fo:table-cell padding="1mm" border="1pt solid black">
                                <fo:block margin-left="10mm" text-align="center">Low</fo:block>
                            </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block margin-left="-8mm" space-before="4pt" text-align="left" font-size="8pt" font-weight="bold">ISSUE DATE:
                    <fo:inline font-weight="normal" th:text="${model.body.issueDate}"> </fo:inline>
                </fo:block>

                <fo:table font-size="8pt" margin-left="-10mm" margin-right="-12mm" width="100%" space-before="40pt">
                    <fo:table-column column-width="65mm"/>
                    <fo:table-column column-width="65mm"/>
                    <fo:table-column column-width="65mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell padding="1mm" display-align="center" border-bottom="0.5pt solid black">
                                <fo:block text-align="center" font-weight="bold">CLASS TEACHER</fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="1mm" display-align="center" border-bottom="0.5pt solid black">
                                <fo:block text-align="center" font-weight="bold">PRINCIPAL</fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="1mm" display-align="center" border-bottom="0.5pt solid black">
                                <fo:block text-align="center" font-weight="bold">PARENT</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>


            </fo:block>
        </fo:flow>
    </fo:page-sequence>

</fo:root>