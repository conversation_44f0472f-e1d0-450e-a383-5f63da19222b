package com.wexl.chatbot;

import com.google.cloud.dialogflow.cx.v3.*;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DialogflowService {

  private final SessionsClient client;
  private final String project = "wexl-voice";
  private final String location = "asia-south1";
  private final String agent = "c7408ee3-3e14-4297-8272-599728fe436d";

  public DialogflowService() throws IOException {
    SessionsSettings.Builder settingsBuilder = SessionsSettings.newBuilder();
    settingsBuilder.setEndpoint("asia-south1-dialogflow.googleapis.com:443");
    SessionsSettings settings = settingsBuilder.build();

    this.client = SessionsClient.create(settings);
  }

  public String detectIntent(String sessionId, String text) {
    SessionName session =
        SessionName.ofProjectLocationAgentSessionName(project, location, agent, sessionId);

    TextInput input = TextInput.newBuilder().setText(text).build();
    QueryInput queryInput = QueryInput.newBuilder().setText(input).setLanguageCode("en").build();
    DetectIntentRequest request =
        DetectIntentRequest.newBuilder()
            .setSession(session.toString())
            .setQueryInput(queryInput)
            .build();
    DetectIntentResponse res = client.detectIntent(request);
    QueryResult queryResult = res.getQueryResult();
    log.info("Query text: " + queryResult.getText());
    log.info("Intent detected: " + queryResult.getIntent().getDisplayName());
    if (queryResult.getResponseMessagesCount() > 0
        && queryResult.getResponseMessages(0).hasText()) {
      return queryResult.getResponseMessages(0).getText().getText(0);
    } else {
      log.info("No text response found");
      return "I'm not sure how to respond to that.";
    }
  }
}
