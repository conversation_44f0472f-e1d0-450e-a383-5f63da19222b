package com.wexl.retail.student.medical.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.guardian.dto.GuardianRequest;
import lombok.Builder;

public record MedicalRequest() {
  @Builder
  public record request(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("blood_group") String bloodGroup,
      @JsonProperty("height_cm") String height,
      @JsonProperty("weight_kg") String weight,
      @JsonProperty("guardians") GuardianRequest guardians,
      @JsonProperty("allergies") Allergies allergies,
      @<PERSON>son<PERSON>roperty("illness") Illness illness,
      @JsonProperty("remarks") <PERSON><PERSON> remarks) {}
}
