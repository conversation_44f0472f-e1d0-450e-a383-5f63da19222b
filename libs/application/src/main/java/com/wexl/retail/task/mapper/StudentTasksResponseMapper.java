package com.wexl.retail.task.mapper;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.task.domain.TaskInst;
import com.wexl.retail.task.dto.StudentTasksResponse;
import java.time.LocalDateTime;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper
public interface StudentTasksResponseMapper {
  StudentTasksResponseMapper mapper = Mappers.getMapper(StudentTasksResponseMapper.class);

  @Mapping(target = "name", source = "taskInst.task.name")
  @Mapping(target = "status", source = "taskInst.completionStatus")
  @Mapping(target = "description", source = "taskInst.task.description")
  @Mapping(target = "taskType", source = "taskInst.task.taskType")
  @Mapping(
      target = "completedAt",
      source = "taskInst.completedAt",
      qualifiedByName = "localDateTimeToLong")
  @Mapping(target = "subjectName", source = "taskInst.task.subjectName")
  @Mapping(target = "subjectSlug", source = "taskInst.task.subjectSlug")
  @Mapping(target = "chapterSlug", source = "taskInst.task.chapterSlug")
  @Mapping(target = "chapterName", source = "taskInst.task.chapterName")
  @Mapping(
      target = "classRoomName",
      source = "taskInst.task.classroomScheduleInst.classroomSchedule.classroom.name")
  @Mapping(
      target = "classRoomId",
      source = "taskInst.task.classroomScheduleInst.classroomSchedule.classroom.id")
  @Mapping(target = "percentage", source = "taskInst.exam", qualifiedByName = "calcExamPercentage")
  @Mapping(target = "taskInstId", source = "taskInst.id")
  @Mapping(target = "taskId", source = "taskInst.task.id")
  @Mapping(target = "subTopicName", source = "taskInst.task.subtopicName")
  @Mapping(target = "subTopicSlug", source = "taskInst.task.subtopicSlug")
  @Mapping(target = "subTopicId", source = "taskInst.task.subtopicId")
  @Mapping(target = "videoSlug", source = "taskInst.task.videoSlug")
  @Mapping(target = "altVimeoLink", source = "taskInst.task.altVimeoLink")
  @Mapping(target = "videoSource", source = "taskInst.task.videoSource")
  @Mapping(target = "videoTitle", source = "taskInst.task.videoTitle")
  @Mapping(target = "synopsisSlug", source = "taskInst.task.synopsisSlug")
  @Mapping(target = "questionCount", source = "taskInst.task.questionCount")
  @Mapping(target = "synopsisTitle", source = "taskInst.task.synopsisTitle")
  @Mapping(target = "examId", source = "taskInst.exam.id")
  @Mapping(target = "assignmentId", source = "taskInst.task.testDefinition.id")
  @Mapping(
      target = "scheduledDate",
      source = "taskInst.task.classroomScheduleInst.startTime",
      qualifiedByName = "localDateTimeToLong")
  StudentTasksResponse taskInstToStudentTasksResponse(TaskInst taskInst);

  @Named("localDateTimeToLong")
  public static Long localDateTimeToLong(LocalDateTime localDateTime) {
    return Objects.nonNull(localDateTime) ? convertIso8601ToEpoch(localDateTime) : null;
  }

  @Named("calcExamPercentage")
  public static float calcExamPercentage(Exam exam) {
    if (exam != null) {
      return exam.getTotalMarks() != 0 ? (exam.getMarksScored() / exam.getTotalMarks()) * 100 : 0f;
    }

    return 0f;
  }
}
