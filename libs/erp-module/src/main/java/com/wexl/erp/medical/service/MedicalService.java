package com.wexl.erp.medical.service;

import com.wexl.erp.medical.dto.MedicalProfileDto;
import com.wexl.erp.medical.model.MedicalHistory;
import com.wexl.erp.medical.repository.MedicalRepository;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.telegram.service.UserService;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MedicalService {

  private final MedicalRepository medicalRepository;
  private final UserRepository userRepository;
  private final UserService userService;
  private final ReportCardService reportCardService;

  public void createMedicalHistory(
      String orgSlug, String studentAuthId, MedicalProfileDto.Request request) {
    MedicalHistory medicalProfile = new MedicalHistory();
    var user = userRepository.findByAuthUserId(studentAuthId).orElseThrow();
    var student = user.getStudentInfo();
    Optional<MedicalHistory> existingMedicalProfile =
        medicalRepository.findByStudentId(student.getId());
    if (existingMedicalProfile.isPresent()) {
      medicalProfile = existingMedicalProfile.get();
    }
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(user.getStudentInfo(), "date_of_birth");

    medicalProfile.setStudentName(userService.getNameByUserInfo(user));
    medicalProfile.setStudentId(student.getId());
    medicalProfile.setDateOfBirth(
        dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null));
    medicalProfile.setOrgSlug(orgSlug);
    medicalProfile.setBloodGroup(request.bloodGroup());
    medicalProfile.setHeight(request.height());
    medicalProfile.setWeight(request.weight());
    medicalProfile.setGuardians(request.guardianDetails());
    medicalProfile.setDrugOrMedicines(request.drugOrMedicines());
    medicalProfile.setFoodAllergy(request.foodAllergy());
    medicalProfile.setChronicDiseases(request.chronicDiseases());
    medicalProfile.setHeartCondition(request.heartCondition());
    medicalProfile.setSurgeryOrAdmittedHospital(request.surgeryOrAdmittedHospital());
    medicalProfile.setWearsSpectacles(request.wearsSpectacles());
    medicalProfile.setDentalTreatment(request.dentalTreatment());
    medicalProfile.setAllergies(request.allergies());
    medicalProfile.setIllness(request.illness());
    medicalProfile.setRemarks(request.remarks());
    medicalRepository.save(medicalProfile);
  }

  public MedicalProfileDto.Response getMedicalHistory(String studentAuthId) {
    var user = userRepository.findByAuthUserId(studentAuthId).orElseThrow();
    Optional<MedicalHistory> medicalProfile =
        medicalRepository.findByStudentId(user.getStudentInfo().getId());
    if (medicalProfile.isEmpty()) {
      return MedicalProfileDto.Response.builder().build();
    }
    return buildMedicalProfileResponse(medicalProfile.get());
  }

  public List<MedicalProfileDto.Response> getMedicalHistoryOfOrg(String orgSlug) {
    var medicalProfiles = medicalRepository.findAllByOrgSlug(orgSlug);
    return medicalProfiles.stream().map(this::buildMedicalProfileResponse).toList();
  }

  private MedicalProfileDto.Response buildMedicalProfileResponse(MedicalHistory medicalProfile) {
    return MedicalProfileDto.Response.builder()
        .studentId(medicalProfile.getStudentId())
        .studentName(medicalProfile.getStudentName())
        .bloodGroup(medicalProfile.getBloodGroup())
        .height(medicalProfile.getHeight())
        .weight(medicalProfile.getWeight())
        .drugOrMedicines(medicalProfile.getDrugOrMedicines())
        .foodAllergy(medicalProfile.getFoodAllergy())
        .chronicDiseases(medicalProfile.getChronicDiseases())
        .heartCondition(medicalProfile.getHeartCondition())
        .surgeryOrAdmittedHospital(medicalProfile.getSurgeryOrAdmittedHospital())
        .wearsSpectacles(medicalProfile.getWearsSpectacles())
        .dentalTreatment(medicalProfile.getDentalTreatment())
        .allergies(medicalProfile.getAllergies())
        .illness(medicalProfile.getIllness())
        .remarks(medicalProfile.getRemarks())
        .build();
  }
}
